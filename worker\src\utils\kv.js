/**
 * KV storage utilities for tasks and logs
 */

/**
 * Task KV operations
 */
export class TaskKV {
  constructor(kv) {
    this.kv = kv;
  }

  /**
   * Gets a task by ID
   * @param {string} taskId - Task ID
   * @returns {Promise<Object|null>} - Task object or null if not found
   */
  async getTask(taskId) {
    try {
      const key = `task:${taskId}`;
      const value = await this.kv.get(key, 'json');
      return value;
    } catch (error) {
      console.error(`Error getting task ${taskId}:`, error);
      return null;
    }
  }

  /**
   * Saves a task
   * @param {Object} task - Task object
   * @returns {Promise<boolean>} - Success status
   */
  async saveTask(task) {
    try {
      const key = `task:${task.id}`;
      await this.kv.put(key, JSON.stringify(task));
      return true;
    } catch (error) {
      console.error(`Error saving task ${task.id}:`, error);
      return false;
    }
  }

  /**
   * Deletes a task
   * @param {string} taskId - Task ID
   * @returns {Promise<boolean>} - Success status
   */
  async deleteTask(taskId) {
    try {
      const key = `task:${taskId}`;
      await this.kv.delete(key);
      return true;
    } catch (error) {
      console.error(`Error deleting task ${taskId}:`, error);
      return false;
    }
  }

  /**
   * Gets all tasks
   * @returns {Promise<Array>} - Array of task objects
   */
  async getAllTasks() {
    try {
      const list = await this.kv.list({ prefix: 'task:' });
      const tasks = [];

      for (const key of list.keys) {
        const task = await this.kv.get(key.name, 'json');
        if (task) {
          tasks.push(task);
        }
      }

      return tasks;
    } catch (error) {
      console.error('Error getting all tasks:', error);
      return [];
    }
  }

  /**
   * Gets active tasks (status = 'active')
   * @returns {Promise<Array>} - Array of active task objects
   */
  async getActiveTasks() {
    const allTasks = await this.getAllTasks();
    return allTasks.filter(task => task.status === 'active');
  }

  /**
   * Updates task's last run time
   * @param {string} taskId - Task ID
   * @param {Date} timestamp - Last run timestamp
   * @returns {Promise<boolean>} - Success status
   */
  async updateLastRunTime(taskId, timestamp) {
    try {
      const task = await this.getTask(taskId);
      if (!task) {
        return false;
      }

      task.lastRunAt = timestamp.toISOString();
      task.updatedAt = new Date().toISOString();
      
      return await this.saveTask(task);
    } catch (error) {
      console.error(`Error updating last run time for task ${taskId}:`, error);
      return false;
    }
  }

  /**
   * Updates task's next run time
   * @param {string} taskId - Task ID
   * @param {Date} timestamp - Next run timestamp
   * @returns {Promise<boolean>} - Success status
   */
  async updateNextRunTime(taskId, timestamp) {
    try {
      const task = await this.getTask(taskId);
      if (!task) {
        return false;
      }

      task.nextRunAt = timestamp.toISOString();
      task.updatedAt = new Date().toISOString();
      
      return await this.saveTask(task);
    } catch (error) {
      console.error(`Error updating next run time for task ${taskId}:`, error);
      return false;
    }
  }
}

/**
 * Log KV operations (circular buffer implementation)
 */
export class LogKV {
  constructor(kv, maxLogs = 50) {
    this.kv = kv;
    this.maxLogs = maxLogs;
  }

  /**
   * Adds a log entry
   * @param {Object} logEntry - Log entry object
   * @returns {Promise<boolean>} - Success status
   */
  async addLog(logEntry) {
    try {
      // Get current log index
      let index = await this.kv.get('log_index', 'json');
      if (index === null) {
        index = 0;
      }

      // Save log entry
      const key = `log:${index}`;
      await this.kv.put(key, JSON.stringify(logEntry));

      // Update index (circular buffer)
      const nextIndex = (index + 1) % this.maxLogs;
      await this.kv.put('log_index', JSON.stringify(nextIndex));

      return true;
    } catch (error) {
      console.error('Error adding log entry:', error);
      return false;
    }
  }

  /**
   * Gets recent logs
   * @param {number} limit - Maximum number of logs to return
   * @returns {Promise<Array>} - Array of log entries (newest first)
   */
  async getRecentLogs(limit = 50) {
    try {
      const logs = [];
      
      // Get current index
      let currentIndex = await this.kv.get('log_index', 'json');
      if (currentIndex === null) {
        return logs;
      }

      // Read logs in reverse order (newest first)
      for (let i = 0; i < Math.min(limit, this.maxLogs); i++) {
        const index = (currentIndex - 1 - i + this.maxLogs) % this.maxLogs;
        const key = `log:${index}`;
        const log = await this.kv.get(key, 'json');
        
        if (log) {
          logs.push(log);
        }
      }

      return logs;
    } catch (error) {
      console.error('Error getting recent logs:', error);
      return [];
    }
  }

  /**
   * Gets logs for a specific task
   * @param {string} taskId - Task ID
   * @param {number} limit - Maximum number of logs to return
   * @returns {Promise<Array>} - Array of log entries for the task
   */
  async getTaskLogs(taskId, limit = 20) {
    const allLogs = await this.getRecentLogs(this.maxLogs);
    return allLogs
      .filter(log => log.taskId === taskId)
      .slice(0, limit);
  }

  /**
   * Gets failure logs only
   * @param {number} limit - Maximum number of logs to return
   * @returns {Promise<Array>} - Array of failure log entries
   */
  async getFailureLogs(limit = 50) {
    const allLogs = await this.getRecentLogs(this.maxLogs);
    return allLogs
      .filter(log => log.status === 'failure' || log.status === 'timeout')
      .slice(0, limit);
  }

  /**
   * Clears all logs (for maintenance)
   * @returns {Promise<boolean>} - Success status
   */
  async clearLogs() {
    try {
      // Delete all log entries
      for (let i = 0; i < this.maxLogs; i++) {
        const key = `log:${i}`;
        await this.kv.delete(key);
      }

      // Reset index
      await this.kv.put('log_index', JSON.stringify(0));
      
      return true;
    } catch (error) {
      console.error('Error clearing logs:', error);
      return false;
    }
  }
}

/**
 * General KV utilities
 */
export class GeneralKV {
  constructor(kv) {
    this.kv = kv;
  }

  /**
   * Gets a value with optional default
   * @param {string} key - Key
   * @param {any} defaultValue - Default value if key not found
   * @param {string} type - Value type ('text', 'json', 'arrayBuffer', 'stream')
   * @returns {Promise<any>} - Value or default
   */
  async get(key, defaultValue = null, type = 'json') {
    try {
      const value = await this.kv.get(key, type);
      return value !== null ? value : defaultValue;
    } catch (error) {
      console.error(`Error getting key ${key}:`, error);
      return defaultValue;
    }
  }

  /**
   * Sets a value
   * @param {string} key - Key
   * @param {any} value - Value
   * @param {Object} options - KV put options
   * @returns {Promise<boolean>} - Success status
   */
  async set(key, value, options = {}) {
    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      await this.kv.put(key, serializedValue, options);
      return true;
    } catch (error) {
      console.error(`Error setting key ${key}:`, error);
      return false;
    }
  }

  /**
   * Deletes a key
   * @param {string} key - Key
   * @returns {Promise<boolean>} - Success status
   */
  async delete(key) {
    try {
      await this.kv.delete(key);
      return true;
    } catch (error) {
      console.error(`Error deleting key ${key}:`, error);
      return false;
    }
  }

  /**
   * Lists keys with optional prefix
   * @param {string} prefix - Key prefix
   * @param {number} limit - Maximum number of keys to return
   * @returns {Promise<Array>} - Array of key objects
   */
  async list(prefix = '', limit = 1000) {
    try {
      const result = await this.kv.list({ prefix, limit });
      return result.keys;
    } catch (error) {
      console.error(`Error listing keys with prefix ${prefix}:`, error);
      return [];
    }
  }
}
