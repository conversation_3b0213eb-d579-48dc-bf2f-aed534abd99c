/**
 * HTTP Request Executor
 */

export const HttpRequestExecutor = {
  type: 'http_request',
  name: 'HTTP Request',
  description: 'Sends HTTP requests to specified URLs',
  icon: 'Promotion',

  /**
   * Configuration schema for frontend form generation
   */
  configSchema: {
    type: 'object',
    required: ['url', 'method'],
    properties: {
      url: {
        type: 'string',
        title: 'URL',
        description: 'Target URL for the HTTP request',
        format: 'uri'
      },
      method: {
        type: 'string',
        title: 'HTTP Method',
        description: 'HTTP method to use',
        enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'],
        default: 'GET'
      },
      headers: {
        type: 'object',
        title: 'Headers',
        description: 'HTTP headers to include',
        additionalProperties: {
          type: 'string'
        }
      },
      body: {
        type: 'string',
        title: 'Request Body',
        description: 'Request body (for POST, PUT, PATCH methods)',
        format: 'textarea'
      },
      followRedirects: {
        type: 'boolean',
        title: 'Follow Redirects',
        description: 'Whether to follow HTTP redirects',
        default: true
      },
      validateSSL: {
        type: 'boolean',
        title: 'Validate SSL',
        description: 'Whether to validate SSL certificates',
        default: true
      },
      expectedStatus: {
        type: 'array',
        title: 'Expected Status Codes',
        description: 'HTTP status codes that indicate success',
        items: {
          type: 'integer'
        },
        default: [200, 201, 202, 204]
      }
    }
  },

  /**
   * Validates configuration
   * @param {Object} config - Configuration to validate
   * @returns {Object} - Validation result
   */
  validateConfig(config) {
    const errors = [];

    // Validate URL
    if (!config.url) {
      errors.push('URL is required');
    } else {
      try {
        new URL(config.url);
      } catch (error) {
        errors.push('Invalid URL format');
      }
    }

    // Validate method
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
    if (!config.method) {
      errors.push('HTTP method is required');
    } else if (!validMethods.includes(config.method.toUpperCase())) {
      errors.push(`Invalid HTTP method: ${config.method}`);
    }

    // Validate headers
    if (config.headers && typeof config.headers !== 'object') {
      errors.push('Headers must be an object');
    }

    // Validate body for methods that support it
    const methodsWithBody = ['POST', 'PUT', 'PATCH'];
    if (config.body && !methodsWithBody.includes(config.method?.toUpperCase())) {
      errors.push(`HTTP method ${config.method} does not support request body`);
    }

    // Validate expected status codes
    if (config.expectedStatus) {
      if (!Array.isArray(config.expectedStatus)) {
        errors.push('Expected status codes must be an array');
      } else {
        for (const status of config.expectedStatus) {
          if (!Number.isInteger(status) || status < 100 || status > 599) {
            errors.push(`Invalid status code: ${status}`);
          }
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * Executes the HTTP request
   * @param {Object} config - Task configuration
   * @param {Object} env - Environment bindings
   * @returns {Promise<Object>} - Execution result
   */
  async execute(config, env) {
    const startTime = Date.now();

    try {
      // Prepare request options
      const requestOptions = {
        method: config.method.toUpperCase(),
        headers: {
          'User-Agent': 'Cron-Task-Worker/1.0',
          ...config.headers
        }
      };

      // Add body for methods that support it
      const methodsWithBody = ['POST', 'PUT', 'PATCH'];
      if (methodsWithBody.includes(requestOptions.method) && config.body) {
        requestOptions.body = config.body;

        // Set content-type if not specified
        if (!requestOptions.headers['Content-Type'] && !requestOptions.headers['content-type']) {
          // Try to detect content type
          try {
            JSON.parse(config.body);
            requestOptions.headers['Content-Type'] = 'application/json';
          } catch {
            requestOptions.headers['Content-Type'] = 'text/plain';
          }
        }
      }

      // Configure redirect behavior
      if (config.followRedirects === false) {
        requestOptions.redirect = 'manual';
      }

      console.log(`Making HTTP request: ${requestOptions.method} ${config.url}`);

      // Make the request
      const response = await fetch(config.url, requestOptions);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Read response body
      let responseBody;
      const contentType = response.headers.get('content-type') || '';

      try {
        if (contentType.includes('application/json')) {
          responseBody = await response.json();
        } else {
          responseBody = await response.text();
        }
      } catch (error) {
        responseBody = `Error reading response: ${error.message}`;
      }

      // Check if status code is expected
      const expectedStatus = config.expectedStatus || [200, 201, 202, 204];
      const isSuccess = expectedStatus.includes(response.status);

      const result = {
        success: isSuccess,
        request: {
          url: config.url,
          method: requestOptions.method,
          headers: requestOptions.headers,
          body: requestOptions.body
        },
        response: {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          body: responseBody
        },
        timing: {
          startTime: new Date(startTime).toISOString(),
          endTime: new Date(endTime).toISOString(),
          duration
        }
      };

      if (!isSuccess) {
        throw new Error(`HTTP request failed with status ${response.status}: ${response.statusText}`);
      }

      console.log(`HTTP request completed successfully in ${duration}ms`);
      return result;

    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;

      console.error('HTTP request failed:', error);

      // Return detailed error information
      const result = {
        success: false,
        request: {
          url: config.url,
          method: config.method,
          headers: config.headers,
          body: config.body
        },
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack
        },
        timing: {
          startTime: new Date(startTime).toISOString(),
          endTime: new Date(endTime).toISOString(),
          duration
        }
      };

      throw new Error(`HTTP request execution failed: ${error.message}`);
    }
  }
};
