/**
 * Executor Registry - Manages task executors
 */

import { HttpRequestExecutor } from './http-request.js';
import { WebhookExecutor } from './webhook.js';
import { EmailExecutor } from './email.js';

/**
 * Executor Registry class
 */
export class ExecutorRegistry {
  static executors = new Map();

  /**
   * Registers an executor
   * @param {Object} executor - Executor object
   */
  static register(executor) {
    if (!executor.type || !executor.name || !executor.execute) {
      throw new Error('Invalid executor: must have type, name, and execute function');
    }

    this.executors.set(executor.type, executor);
    console.log(`Registered executor: ${executor.type} (${executor.name})`);
  }

  /**
   * Gets an executor by type
   * @param {string} type - Executor type
   * @returns {Object|null} - Executor or null if not found
   */
  static getExecutor(type) {
    return this.executors.get(type) || null;
  }

  /**
   * Gets all registered executors
   * @returns {Array} - Array of executor objects
   */
  static getAllExecutors() {
    return Array.from(this.executors.values());
  }

  /**
   * Gets executor metadata for frontend
   * @returns {Array} - Array of executor metadata
   */
  static getExecutorMetadata() {
    return Array.from(this.executors.values()).map(executor => ({
      type: executor.type,
      name: executor.name,
      description: executor.description,
      configSchema: executor.configSchema,
      icon: executor.icon
    }));
  }

  /**
   * Validates executor configuration
   * @param {string} type - Executor type
   * @param {Object} config - Configuration to validate
   * @returns {Object} - Validation result { valid: boolean, errors: array }
   */
  static validateConfig(type, config) {
    const executor = this.getExecutor(type);
    if (!executor) {
      return {
        valid: false,
        errors: [`Unknown executor type: ${type}`]
      };
    }

    if (executor.validateConfig) {
      return executor.validateConfig(config);
    }

    // Basic validation if no custom validator
    const schema = executor.configSchema;
    if (!schema) {
      return { valid: true, errors: [] };
    }

    const errors = [];
    
    // Check required fields
    if (schema.required) {
      for (const field of schema.required) {
        if (!config[field]) {
          errors.push(`Missing required field: ${field}`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Initializes all executors
   */
  static initialize() {
    // Register built-in executors
    this.register(HttpRequestExecutor);
    this.register(WebhookExecutor);
    this.register(EmailExecutor);

    console.log(`Initialized ${this.executors.size} executors`);
  }
}

// Initialize executors when module is loaded
ExecutorRegistry.initialize();
