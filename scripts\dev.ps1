# Development script for Cron Task Manager
# This script starts both frontend and backend in development mode

param(
    [switch]$FrontendOnly = $false,
    [switch]$BackendOnly = $false,
    [switch]$Install = $false
)

Write-Host "🛠️ Starting development environment..." -ForegroundColor Green

# Set error action preference
$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$RootDir = Split-Path -Parent $ScriptDir

try {
    # Install dependencies if requested
    if ($Install) {
        Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow

        # Install backend dependencies
        if (-not $FrontendOnly) {
            Write-Host "Installing backend dependencies..." -ForegroundColor Blue
            Set-Location "$RootDir\worker"
            pnpm install
        }

        # Install frontend dependencies
        if (-not $BackendOnly) {
            Write-Host "Installing frontend dependencies..." -ForegroundColor Blue
            Set-Location "$RootDir\frontend"
            pnpm install
        }

        Set-Location $RootDir
        Write-Host "✅ Dependencies installed!" -ForegroundColor Green
    }

    # Start development servers
    if ($BackendOnly) {
        # Start only backend
        Write-Host "🚀 Starting backend development server..." -ForegroundColor Yellow
        Set-Location "$RootDir\worker"
        pnpm run dev

    } elseif ($FrontendOnly) {
        # Start only frontend
        Write-Host "🎨 Starting frontend development server..." -ForegroundColor Yellow
        Set-Location "$RootDir\frontend"
        pnpm run dev

    } else {
        # Start both frontend and backend
        Write-Host "🚀 Starting both frontend and backend..." -ForegroundColor Yellow

        # Start backend in background
        Write-Host "Starting backend server..." -ForegroundColor Blue
        $BackendJob = Start-Job -ScriptBlock {
            Set-Location $using:RootDir\worker
            pnpm run dev
        }

        # Wait a moment for backend to start
        Start-Sleep -Seconds 3

        # Start frontend
        Write-Host "Starting frontend server..." -ForegroundColor Blue
        Set-Location "$RootDir\frontend"
        pnpm run dev

        # Clean up background job when script exits
        if ($BackendJob) {
            Stop-Job $BackendJob
            Remove-Job $BackendJob
        }
    }

} catch {
    Write-Host "❌ Development server failed to start: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    # Return to original directory
    Set-Location $RootDir
}

Write-Host ""
Write-Host "📋 Development URLs:" -ForegroundColor Cyan
Write-Host "Frontend: http://localhost:5173" -ForegroundColor White
Write-Host "Backend: http://localhost:8787" -ForegroundColor White
