/**
 * JWT utilities for authentication
 */

import { SignJWT, jwtVerify } from 'jose';

/**
 * JWT utility class
 */
export class JWTUtil {
  constructor(secret) {
    this.secret = new TextEncoder().encode(secret);
  }

  /**
   * Creates a JWT token
   * @param {Object} payload - Token payload
   * @param {string} expiresIn - Expiration time (e.g., '24h', '7d')
   * @returns {Promise<string>} - JWT token
   */
  async createToken(payload, expiresIn = '24h') {
    try {
      const jwt = await new SignJWT(payload)
        .setProtectedHeader({ alg: 'HS256' })
        .setIssuedAt()
        .setExpirationTime(expiresIn)
        .setIssuer('cron-task-worker')
        .setAudience('cron-task-frontend')
        .sign(this.secret);

      return jwt;
    } catch (error) {
      console.error('Error creating JWT token:', error);
      throw new Error('Failed to create token');
    }
  }

  /**
   * Verifies a JWT token
   * @param {string} token - JWT token
   * @returns {Promise<Object>} - Decoded payload
   */
  async verifyToken(token) {
    try {
      const { payload } = await jwtVerify(token, this.secret, {
        issuer: 'cron-task-worker',
        audience: 'cron-task-frontend'
      });

      return payload;
    } catch (error) {
      console.error('Error verifying JWT token:', error);
      throw new Error('Invalid token');
    }
  }

  /**
   * Extracts token from Authorization header
   * @param {string} authHeader - Authorization header value
   * @returns {string|null} - Token or null if not found
   */
  extractTokenFromHeader(authHeader) {
    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  }

  /**
   * Creates a login token
   * @param {string} userId - User ID
   * @param {Object} additionalClaims - Additional claims to include
   * @returns {Promise<string>} - JWT token
   */
  async createLoginToken(userId, additionalClaims = {}) {
    const payload = {
      sub: userId,
      type: 'login',
      ...additionalClaims
    };

    return await this.createToken(payload, '24h');
  }

  /**
   * Verifies a login token
   * @param {string} token - JWT token
   * @returns {Promise<Object>} - User information
   */
  async verifyLoginToken(token) {
    const payload = await this.verifyToken(token);
    
    if (payload.type !== 'login') {
      throw new Error('Invalid token type');
    }

    return {
      userId: payload.sub,
      ...payload
    };
  }
}

/**
 * Password hashing utilities
 */
export class PasswordUtil {
  /**
   * Hashes a password using Web Crypto API
   * @param {string} password - Plain text password
   * @param {string} salt - Salt (optional, will generate if not provided)
   * @returns {Promise<Object>} - Hash and salt
   */
  async hashPassword(password, salt = null) {
    try {
      // Generate salt if not provided
      if (!salt) {
        const saltArray = new Uint8Array(16);
        crypto.getRandomValues(saltArray);
        salt = Array.from(saltArray, byte => byte.toString(16).padStart(2, '0')).join('');
      }

      // Create key from password and salt
      const encoder = new TextEncoder();
      const passwordData = encoder.encode(password + salt);
      
      // Hash using SHA-256
      const hashBuffer = await crypto.subtle.digest('SHA-256', passwordData);
      const hashArray = new Uint8Array(hashBuffer);
      const hash = Array.from(hashArray, byte => byte.toString(16).padStart(2, '0')).join('');

      return { hash, salt };
    } catch (error) {
      console.error('Error hashing password:', error);
      throw new Error('Failed to hash password');
    }
  }

  /**
   * Verifies a password against a hash
   * @param {string} password - Plain text password
   * @param {string} hash - Stored hash
   * @param {string} salt - Stored salt
   * @returns {Promise<boolean>} - Whether password matches
   */
  async verifyPassword(password, hash, salt) {
    try {
      const { hash: computedHash } = await this.hashPassword(password, salt);
      return computedHash === hash;
    } catch (error) {
      console.error('Error verifying password:', error);
      return false;
    }
  }

  /**
   * Simple password verification (for single password setup)
   * @param {string} inputPassword - Input password
   * @param {string} storedPassword - Stored password (plain text for simplicity)
   * @returns {boolean} - Whether passwords match
   */
  verifySimplePassword(inputPassword, storedPassword) {
    return inputPassword === storedPassword;
  }
}

/**
 * Rate limiting utilities
 */
export class RateLimiter {
  constructor(kv) {
    this.kv = kv;
  }

  /**
   * Checks if an IP is rate limited
   * @param {string} ip - IP address
   * @param {number} maxAttempts - Maximum attempts allowed
   * @param {number} windowMs - Time window in milliseconds
   * @returns {Promise<Object>} - Rate limit status
   */
  async checkRateLimit(ip, maxAttempts = 5, windowMs = 15 * 60 * 1000) {
    try {
      const key = `rate_limit:${ip}`;
      const now = Date.now();
      
      // Get current attempts
      const data = await this.kv.get(key, 'json');
      
      if (!data) {
        // First attempt
        await this.kv.put(key, JSON.stringify({
          attempts: 1,
          firstAttempt: now,
          lastAttempt: now
        }), { expirationTtl: Math.ceil(windowMs / 1000) });
        
        return {
          allowed: true,
          remaining: maxAttempts - 1,
          resetTime: now + windowMs
        };
      }

      // Check if window has expired
      if (now - data.firstAttempt > windowMs) {
        // Reset window
        await this.kv.put(key, JSON.stringify({
          attempts: 1,
          firstAttempt: now,
          lastAttempt: now
        }), { expirationTtl: Math.ceil(windowMs / 1000) });
        
        return {
          allowed: true,
          remaining: maxAttempts - 1,
          resetTime: now + windowMs
        };
      }

      // Check if limit exceeded
      if (data.attempts >= maxAttempts) {
        return {
          allowed: false,
          remaining: 0,
          resetTime: data.firstAttempt + windowMs
        };
      }

      // Increment attempts
      const newData = {
        ...data,
        attempts: data.attempts + 1,
        lastAttempt: now
      };
      
      await this.kv.put(key, JSON.stringify(newData), { 
        expirationTtl: Math.ceil((data.firstAttempt + windowMs - now) / 1000) 
      });

      return {
        allowed: true,
        remaining: maxAttempts - newData.attempts,
        resetTime: data.firstAttempt + windowMs
      };

    } catch (error) {
      console.error('Error checking rate limit:', error);
      // Allow request on error
      return {
        allowed: true,
        remaining: maxAttempts - 1,
        resetTime: Date.now() + windowMs
      };
    }
  }

  /**
   * Resets rate limit for an IP
   * @param {string} ip - IP address
   * @returns {Promise<boolean>} - Success status
   */
  async resetRateLimit(ip) {
    try {
      const key = `rate_limit:${ip}`;
      await this.kv.delete(key);
      return true;
    } catch (error) {
      console.error('Error resetting rate limit:', error);
      return false;
    }
  }
}
