/**
 * Error handling middleware
 */

/**
 * Error handling middleware
 * @param {Context} c - Hono context
 * @param {Function} next - Next middleware function
 * @returns {Promise<Response>} - Response
 */
export async function errorHandler(c, next) {
  try {
    await next();
  } catch (error) {
    console.error('Request error:', error);

    // Determine error type and status code
    let status = 500;
    let errorType = 'Internal Server Error';
    let message = error.message || 'An unexpected error occurred';

    // Handle specific error types
    if (error.name === 'ValidationError') {
      status = 400;
      errorType = 'Validation Error';
    } else if (error.name === 'NotFoundError') {
      status = 404;
      errorType = 'Not Found';
    } else if (error.name === 'UnauthorizedError') {
      status = 401;
      errorType = 'Unauthorized';
    } else if (error.name === 'ForbiddenError') {
      status = 403;
      errorType = 'Forbidden';
    } else if (error.name === 'ConflictError') {
      status = 409;
      errorType = 'Conflict';
    } else if (error.name === 'RateLimitError') {
      status = 429;
      errorType = 'Too Many Requests';
    }

    // Create error response
    const errorResponse = {
      error: errorType,
      message: message,
      timestamp: new Date().toISOString(),
      path: c.req.path,
      method: c.req.method
    };

    // Add stack trace in development
    if (c.env.ENVIRONMENT === 'development') {
      errorResponse.stack = error.stack;
    }

    // Add request ID if available
    const requestId = c.req.header('X-Request-ID') || c.req.header('CF-Ray');
    if (requestId) {
      errorResponse.requestId = requestId;
    }

    return c.json(errorResponse, status);
  }
}

/**
 * Custom error classes
 */
export class ValidationError extends Error {
  constructor(message, details = null) {
    super(message);
    this.name = 'ValidationError';
    this.details = details;
  }
}

export class NotFoundError extends Error {
  constructor(message = 'Resource not found') {
    super(message);
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends Error {
  constructor(message = 'Unauthorized') {
    super(message);
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends Error {
  constructor(message = 'Forbidden') {
    super(message);
    this.name = 'ForbiddenError';
  }
}

export class ConflictError extends Error {
  constructor(message = 'Conflict') {
    super(message);
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends Error {
  constructor(message = 'Too many requests') {
    super(message);
    this.name = 'RateLimitError';
  }
}
